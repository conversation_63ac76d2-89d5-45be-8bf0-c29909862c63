package interceptor

/*
#cgo CFLAGS: -I${SRCDIR}/../lib
#cgo LDFLAGS: -L${SRCDIR}/../lib -lwindivert
#include "windivert.h"
*/
import "C"

import (
	"fmt"
	"log"
	"unsafe"
)

// 修改过滤器为只拦截出站TCP流量，排除本地回环
const filter = "outbound and tcp and !loopback"

// PacketMetadata 包含元信息
type PacketMetadata struct {
	SrcIP   string
	DstIP   string
	SrcPort uint16
	DstPort uint16
}

// Start 拦截主循环
func Start(handlePacket func([]byte, *PacketMetadata)) {
	// 使用正确的参数：layer=NETWORK(0), priority=0, flags=0
	handle := C.WinDivertOpen(C.CString(filter), C.WINDIVERT_LAYER_NETWORK, 0, 0)
	if handle == nil {
		log.Fatalf("WinDivertOpen 失败，请确保：\n1. 以管理员权限运行\n2. WinDivert驱动已正确安装")
	}
	defer C.WinDivertClose(handle)

	log.Printf("开始拦截网络流量，过滤器: %s", filter)
	log.Println("如果没有看到流量，请尝试访问网站或发送网络请求...")

	buf := make([]byte, 65535)
	packetCount := 0

	for {
		var addr C.WINDIVERT_ADDRESS
		var recvLen C.UINT

		// 接收数据包
		if C.WinDivertRecv(handle, unsafe.Pointer(&buf[0]), C.UINT(len(buf)), &recvLen, &addr) == 0 {
			continue
		}

		packetCount++
		log.Printf("拦截到第 %d 个数据包，大小: %d 字节", packetCount, recvLen)

		// 复制数据包内容，避免并发问题
		packetData := make([]byte, recvLen)
		copy(packetData, buf[:recvLen])

		// 解析IP头部信息
		meta := parsePacketInfo(packetData, &addr)

		// 异步处理数据包分析
		go func(data []byte, metadata *PacketMetadata) {
			handlePacket(data, metadata)
		}(packetData, meta)

		// 重要：将数据包重新发送回网络栈，确保网络正常工作
		var sendLen C.UINT
		if C.WinDivertSend(handle, unsafe.Pointer(&packetData[0]), C.UINT(len(packetData)), &sendLen, &addr) == 0 {
			log.Printf("警告：数据包转发失败")
		}
	}
}

// parsePacketInfo 解析数据包的IP和端口信息
func parsePacketInfo(packet []byte, addr *C.WINDIVERT_ADDRESS) *PacketMetadata {
	meta := &PacketMetadata{
		SrcIP:   "unknown",
		DstIP:   "unknown",
		SrcPort: 0,
		DstPort: 0,
	}

	// 使用WinDivert的辅助函数解析数据包
	var ipHdr *C.WINDIVERT_IPHDR
	var tcpHdr *C.WINDIVERT_TCPHDR
	var payload unsafe.Pointer
	var payloadLen C.UINT

	if C.WinDivertHelperParsePacket(
		unsafe.Pointer(&packet[0]),
		C.UINT(len(packet)),
		&ipHdr,
		nil, // IPv6
		nil, // Protocol
		nil, // ICMP
		nil, // ICMPv6
		&tcpHdr,
		nil, // UDP
		&payload,
		&payloadLen,
		nil, // Next
		nil, // NextLen
	) != 0 {
		// 成功解析
		if ipHdr != nil {
			meta.SrcIP = formatIPv4(uint32(ipHdr.SrcAddr))
			meta.DstIP = formatIPv4(uint32(ipHdr.DstAddr))
		}
		if tcpHdr != nil {
			meta.SrcPort = uint16(C.ntohs(tcpHdr.SrcPort))
			meta.DstPort = uint16(C.ntohs(tcpHdr.DstPort))
		}
	}

	return meta
}

// formatIPv4 将32位整数转换为IP地址字符串
func formatIPv4(addr uint32) string {
	return fmt.Sprintf("%d.%d.%d.%d",
		(addr)&0xFF,
		(addr>>8)&0xFF,
		(addr>>16)&0xFF,
		(addr>>24)&0xFF)
}
