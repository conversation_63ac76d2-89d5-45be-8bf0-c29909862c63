package interceptor

/*
#cgo CFLAGS: -I${SRCDIR}/../lib
#cgo LDFLAGS: -L${SRCDIR}/../lib -lwindivert
#include "windivert.h"
*/
import "C"

import (
	"fmt"
	"log"
	"unsafe"
)

// 修改过滤器为只拦截出站TCP流量，排除本地回环
const filter = "outbound and tcp and !loopback"

// PacketMetadata 包含元信息
type PacketMetadata struct {
	SrcIP   string
	DstIP   string
	SrcPort uint16
	DstPort uint16
}

// Start 拦截主循环
func Start(handlePacket func([]byte, *PacketMetadata)) {
	log.Printf("尝试打开WinDivert，过滤器: %s", filter)

	// 先检查WinDivert版本
	checkWinDivertVersion()

	// 创建过滤器字符串的C字符串，并确保内存不被GC回收
	filterCStr := C.CString(filter)
	defer C.free(unsafe.Pointer(filterCStr))

	// 使用正确的参数：layer=NETWORK(0), priority=0, flags=0
	handle := C.WinDivertOpen(filterCStr, C.WINDIVERT_LAYER_NETWORK, 0, 0)
	if handle == nil {
		// 获取详细错误信息
		lastError := getLastError()
		log.Printf("WinDivertOpen 失败，错误码: %d", lastError)

		// 尝试更简单的过滤器
		log.Println("尝试使用更简单的过滤器...")
		simpleFilter := "tcp"
		simpleFilterCStr := C.CString(simpleFilter)
		defer C.free(unsafe.Pointer(simpleFilterCStr))

		handle = C.WinDivertOpen(simpleFilterCStr, C.WINDIVERT_LAYER_NETWORK, 0, 0)
		if handle == nil {
			lastError2 := getLastError()
			log.Fatalf("WinDivertOpen 完全失败，错误码: %d\n请确保：\n1. 以管理员权限运行\n2. WinDivert驱动已正确安装\n3. 系统支持WinDivert", lastError2)
		}
		log.Printf("使用简单过滤器成功: %s", simpleFilter)
	} else {
		log.Printf("WinDivert打开成功，过滤器: %s", filter)
	}
	defer C.WinDivertClose(handle)

	log.Println("开始监听网络流量...")
	log.Println("请在另一个终端执行网络操作来测试拦截功能")

	buf := make([]byte, 65535)
	packetCount := 0
	errorCount := 0

	for {
		var addr C.WINDIVERT_ADDRESS
		var recvLen C.UINT

		// 接收数据包 - 不要频繁打印等待信息
		result := C.WinDivertRecv(handle, unsafe.Pointer(&buf[0]), C.UINT(len(buf)), &recvLen, &addr)
		if result == 0 {
			errorCount++
			if errorCount%1000 == 0 { // 每1000次错误打印一次
				lastError := getLastError()
				log.Printf("WinDivertRecv 失败，错误码: %d，累计错误次数: %d", lastError, errorCount)
			}
			continue
		}

		// 重置错误计数
		if errorCount > 0 {
			log.Printf("开始接收数据包，之前累计错误: %d 次", errorCount)
			errorCount = 0
		}

		packetCount++
		log.Printf("✅ 成功拦截到第 %d 个数据包！大小: %d 字节", packetCount, recvLen)

		// 复制数据包内容，避免并发问题
		packetData := make([]byte, recvLen)
		copy(packetData, buf[:recvLen])

		// 解析IP头部信息
		meta := parsePacketInfo(packetData, &addr)
		log.Printf("数据包信息: %s:%d -> %s:%d", meta.SrcIP, meta.SrcPort, meta.DstIP, meta.DstPort)

		// 异步处理数据包分析
		go func(data []byte, metadata *PacketMetadata) {
			handlePacket(data, metadata)
		}(packetData, meta)

		// 重要：将数据包重新发送回网络栈，确保网络正常工作
		var sendLen C.UINT
		sendResult := C.WinDivertSend(handle, unsafe.Pointer(&packetData[0]), C.UINT(len(packetData)), &sendLen, &addr)
		if sendResult == 0 {
			sendError := getLastError()
			log.Printf("⚠️ 警告：数据包转发失败，错误码: %d", sendError)
		} else {
			log.Printf("✅ 数据包已转发，发送字节数: %d", sendLen)
		}
	}
}

// parsePacketInfo 解析数据包的IP和端口信息
func parsePacketInfo(packet []byte, addr *C.WINDIVERT_ADDRESS) *PacketMetadata {
	meta := &PacketMetadata{
		SrcIP:   "unknown",
		DstIP:   "unknown",
		SrcPort: 0,
		DstPort: 0,
	}

	if len(packet) < 20 {
		return meta
	}

	// 简单解析IP头部（前20字节）
	if len(packet) >= 20 {
		// IP头部格式：版本(4bit) + 头长度(4bit) + 服务类型(8bit) + 总长度(16bit) + ...
		// 源IP地址在偏移12-15字节，目标IP在偏移16-19字节
		srcIP := (uint32(packet[12]) << 24) | (uint32(packet[13]) << 16) | (uint32(packet[14]) << 8) | uint32(packet[15])
		dstIP := (uint32(packet[16]) << 24) | (uint32(packet[17]) << 16) | (uint32(packet[18]) << 8) | uint32(packet[19])

		meta.SrcIP = formatIPv4(srcIP)
		meta.DstIP = formatIPv4(dstIP)

		// 获取IP头部长度
		ipHeaderLen := int(packet[0]&0x0F) * 4

		// 检查是否是TCP协议 (协议号6)
		if len(packet) > 9 && packet[9] == 6 && len(packet) >= ipHeaderLen+4 {
			// TCP头部：源端口(16bit) + 目标端口(16bit) + ...
			tcpStart := ipHeaderLen
			if len(packet) >= tcpStart+4 {
				meta.SrcPort = (uint16(packet[tcpStart]) << 8) | uint16(packet[tcpStart+1])
				meta.DstPort = (uint16(packet[tcpStart+2]) << 8) | uint16(packet[tcpStart+3])
			}
		}
	}

	return meta
}

// formatIPv4 将32位整数转换为IP地址字符串
func formatIPv4(addr uint32) string {
	return fmt.Sprintf("%d.%d.%d.%d",
		(addr)&0xFF,
		(addr>>8)&0xFF,
		(addr>>16)&0xFF,
		(addr>>24)&0xFF)
}

// getLastError 获取Windows最后的错误码
func getLastError() uint32 {
	return uint32(C.GetLastError())
}

// checkWinDivertVersion 检查WinDivert版本
func checkWinDivertVersion() {
	// 尝试创建一个临时句柄来测试WinDivert是否可用
	testFilter := C.CString("false") // 永远不匹配的过滤器
	defer C.free(unsafe.Pointer(testFilter))

	testHandle := C.WinDivertOpen(testFilter, C.WINDIVERT_LAYER_NETWORK, 0, 0)
	if testHandle != nil {
		C.WinDivertClose(testHandle)
		log.Println("✅ WinDivert 驱动可用")

		// 尝试获取版本信息
		var majorVersion, minorVersion C.UINT64
		if C.WinDivertGetParam(testHandle, C.WINDIVERT_PARAM_VERSION_MAJOR, &majorVersion) != 0 &&
		   C.WinDivertGetParam(testHandle, C.WINDIVERT_PARAM_VERSION_MINOR, &minorVersion) != 0 {
			log.Printf("WinDivert 版本: %d.%d", majorVersion, minorVersion)
		}
	} else {
		lastError := getLastError()
		log.Printf("❌ WinDivert 驱动不可用，错误码: %d", lastError)

		// 提供详细的错误说明
		switch lastError {
		case 2: // ERROR_FILE_NOT_FOUND
			log.Println("错误：找不到WinDivert驱动文件")
		case 5: // ERROR_ACCESS_DENIED
			log.Println("错误：访问被拒绝，请以管理员权限运行")
		case 1275: // ERROR_DRIVER_FAILED_SLEEP
			log.Println("错误：驱动加载失败")
		case 577: // ERROR_INVALID_USER_BUFFER
			log.Println("错误：无效的用户缓冲区")
		default:
			log.Printf("错误：未知错误码 %d", lastError)
		}
	}
}
