package main

import (
	"database/sql"
	"fmt"
	"log"
	"os"

	_ "github.com/mattn/go-sqlite3"
)

func main() {
	dbPath := "packets.db"
	
	// 检查数据库文件是否存在
	if _, err := os.Stat(dbPath); os.IsNotExist(err) {
		fmt.Printf("数据库文件 %s 不存在\n", dbPath)
		return
	}

	// 打开数据库
	db, err := sql.Open("sqlite3", dbPath)
	if err != nil {
		log.Fatalf("打开数据库失败: %v", err)
	}
	defer db.Close()

	// 查询记录数量
	var count int
	err = db.QueryRow("SELECT COUNT(*) FROM packets").Scan(&count)
	if err != nil {
		log.Fatalf("查询记录数量失败: %v", err)
	}

	fmt.Printf("数据库中共有 %d 条记录\n\n", count)

	if count == 0 {
		fmt.Println("没有拦截到任何数据包")
		return
	}

	// 查询最近的10条记录
	rows, err := db.Query(`
		SELECT id, timestamp, src_ip, dst_ip, src_port, dst_port, 
		       CASE 
		           WHEN LENGTH(payload) > 100 THEN SUBSTR(payload, 1, 100) || '...'
		           ELSE payload 
		       END as payload_preview
		FROM packets 
		ORDER BY timestamp DESC 
		LIMIT 10
	`)
	if err != nil {
		log.Fatalf("查询记录失败: %v", err)
	}
	defer rows.Close()

	fmt.Println("最近的10条拦截记录:")
	fmt.Println("=====================================")
	
	for rows.Next() {
		var id int
		var timestamp, srcIP, dstIP, payload string
		var srcPort, dstPort int

		err := rows.Scan(&id, &timestamp, &srcIP, &dstIP, &srcPort, &dstPort, &payload)
		if err != nil {
			log.Printf("读取记录失败: %v", err)
			continue
		}

		fmt.Printf("ID: %d\n", id)
		fmt.Printf("时间: %s\n", timestamp)
		fmt.Printf("源地址: %s:%d\n", srcIP, srcPort)
		fmt.Printf("目标地址: %s:%d\n", dstIP, dstPort)
		fmt.Printf("载荷预览: %s\n", payload)
		fmt.Println("-------------------------------------")
	}
}
