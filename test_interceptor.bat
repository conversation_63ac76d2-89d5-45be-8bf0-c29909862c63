@echo off
echo 测试网络拦截器
echo.
echo 请确保以管理员权限运行此脚本！
echo.
echo 启动拦截器...
start "NetInterceptor" net-interceptor.exe

echo 等待3秒让拦截器启动...
timeout /t 3 /nobreak >nul

echo.
echo 现在发送一些测试请求...
echo.

echo 1. 测试HTTP请求到baidu.com (应该被拦截)
curl -s "http://www.baidu.com" >nul
echo    HTTP请求已发送

echo.
echo 2. 测试包含关键词的请求
curl -s -d "username=test&password=123456" "http://httpbin.org/post" >nul
echo    包含password关键词的请求已发送

echo.
echo 3. 测试包含token关键词的请求  
curl -s -H "Authorization: Bearer token123456" "http://httpbin.org/headers" >nul
echo    包含token关键词的请求已发送

echo.
echo 测试完成！请检查拦截器输出和数据库文件 packets.db
echo.
pause
